<?php
/**
 * Serviciu pentru interacțiunea cu API-ul SOAP al Portalului Instanțelor de Judecată
 */
class DosarService {
    /**
     * Instanța clientului SOAP
     * @var SoapClient
     */
    private $soapClient;

    /**
     * Numărul maxim de încercări pentru apelurile SOAP
     * @var int
     */
    private $maxRetries = 3;

    /**
     * Timpul de așteptare între reîncercări (în microsecunde)
     * @var int
     */
    private $retryDelay = 500000; // 0.5 secunde

    /**
     * Constructor
     */
    public function __construct() {
        try {
            // Opțiuni pentru clientul SOAP
            $options = [
                'soap_version' => SOAP_1_2,
                'exceptions' => true,
                'trace' => true,
                'cache_wsdl' => WSDL_CACHE_NONE,
                'connection_timeout' => 10, // Timeout de conexiune de 10 secunde
                'features' => SOAP_SINGLE_ELEMENT_ARRAYS // Forțează returnarea array-urilor pentru elemente singulare
            ];

            // Inițializare client SOAP
            $this->soapClient = new SoapClient(SOAP_WSDL, $options);
        } catch (SoapFault $e) {
            throw new Exception("Eroare la conectarea la serviciul SOAP: " . $e->getMessage());
        }
    }

    /**
     * Caută dosare după număr
     *
     * @param string $numarDosar Numărul dosarului
     * @param string $instanta Instanța judecătorească
     * @param string $obiectDosar Obiectul dosarului
     * @param string $dataInceput Data de început (format: d.m.Y)
     * @param string $dataSfarsit Data de sfârșit (format: d.m.Y)
     * @return array Rezultatele căutării
     */
    public function cautareDupaNumarDosar($numarDosar, $instanta = '', $obiectDosar = '', $dataInceput = '', $dataSfarsit = '') {
        try {
            // Handle empty institutie parameter
            $institutie = !empty($instanta) ? $instanta : null;

            $params = [
                'numarDosar' => $numarDosar,
                'obiectDosar' => $obiectDosar,
                'numeParte' => '',
                'institutie' => $institutie,
                'dataStart' => $this->formatDateForSoap($dataInceput),
                'dataStop' => $this->formatDateForSoap($dataSfarsit),
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];

            // Utilizăm mecanismul de reîncercare pentru apelul SOAP
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $params, "Eroare la căutarea după număr dosar");
            return $this->processResponse($response);
        } catch (Exception $e) {
            throw new Exception("Eroare la căutarea după număr dosar: " . $e->getMessage());
        }
    }

    /**
     * Caută dosare după nume parte
     *
     * @param string $numeParte Numele părții
     * @param string $instanta Instanța judecătorească
     * @param string $obiectDosar Obiectul dosarului
     * @param string $dataInceput Data de început (format: d.m.Y)
     * @param string $dataSfarsit Data de sfârșit (format: d.m.Y)
     * @return array Rezultatele căutării
     */
    public function cautareDupaNumeParte($numeParte, $instanta = '', $obiectDosar = '', $dataInceput = '', $dataSfarsit = '') {
        try {
            // Handle empty institutie parameter
            $institutie = !empty($instanta) ? $instanta : null;

            // Normalizăm numele părții pentru a gestiona diacriticele
            $normalizedNumeParte = $this->normalizeDiacritics($numeParte);

            $params = [
                'numarDosar' => '',
                'obiectDosar' => $obiectDosar,
                'numeParte' => $numeParte,
                'institutie' => $institutie,
                'dataStart' => $this->formatDateForSoap($dataInceput),
                'dataStop' => $this->formatDateForSoap($dataSfarsit),
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];

            // Utilizăm mecanismul de reîncercare pentru apelul SOAP
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $params, "Eroare la căutarea după nume parte");
            $results = $this->processResponse($response);

            // Dacă nu am găsit rezultate și numele părții conține diacritice, încercăm cu versiunea normalizată
            if (empty($results) && $numeParte !== $normalizedNumeParte) {
                $normalizedParams = $params;
                $normalizedParams['numeParte'] = $normalizedNumeParte;

                try {
                    $normalizedResponse = $this->executeSoapCallWithRetry('CautareDosare2', $normalizedParams, "Eroare la căutarea după nume parte normalizat");
                    $normalizedResults = $this->processResponse($normalizedResponse);

                    if (!empty($normalizedResults)) {
                        return $normalizedResults;
                    }
                } catch (Exception $e) {
                    // Ignorăm erorile la căutarea cu nume normalizat și returnăm rezultatele originale (goale)
                }
            }

            return $results;
        } catch (Exception $e) {
            throw new Exception("Eroare la căutarea după nume parte: " . $e->getMessage());
        }
    }

    /**
     * Caută dosare după obiect
     *
     * @param string $obiectDosar Obiectul dosarului
     * @param string $instanta Instanța judecătorească
     * @param string $dataInceput Data de început (format: d.m.Y)
     * @param string $dataSfarsit Data de sfârșit (format: d.m.Y)
     * @return array Rezultatele căutării
     */
    public function cautareDupaObiect($obiectDosar, $instanta = '', $dataInceput = '', $dataSfarsit = '') {
        try {
            // Handle empty institutie parameter
            $institutie = !empty($instanta) ? $instanta : null;

            $params = [
                'numarDosar' => '',
                'obiectDosar' => $obiectDosar,
                'numeParte' => '',
                'institutie' => $institutie,
                'dataStart' => $this->formatDateForSoap($dataInceput),
                'dataStop' => $this->formatDateForSoap($dataSfarsit),
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];

            // Utilizăm mecanismul de reîncercare pentru apelul SOAP
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $params, "Eroare la căutarea după obiect dosar");
            return $this->processResponse($response);
        } catch (Exception $e) {
            throw new Exception("Eroare la căutarea după obiect dosar: " . $e->getMessage());
        }
    }

    /**
     * Execută un apel SOAP cu mecanism de reîncercare
     *
     * @param string $method Metoda SOAP care va fi apelată
     * @param array $params Parametrii pentru apelul SOAP
     * @param string $errorPrefix Prefixul pentru mesajul de eroare
     * @return mixed Răspunsul de la apelul SOAP
     * @throws Exception Dacă apelul eșuează după toate reîncercările
     */
    private function executeSoapCallWithRetry($method, $params, $errorPrefix = "Eroare SOAP") {
        $attempt = 0;
        $lastException = null;
        $logDir = __DIR__ . '/../logs';

        // Asigurăm-ne că directorul de loguri există
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = "{$logDir}/soap_calls.log";

        while ($attempt < $this->maxRetries) {
            try {
                // Incrementăm numărul de încercări
                $attempt++;

                // Logăm încercarea curentă
                $logData = date('Y-m-d H:i:s') . " - Încercare {$attempt}/{$this->maxRetries} pentru metoda {$method}\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                // Executăm apelul SOAP
                $response = $this->soapClient->$method($params);

                // Dacă am ajuns aici, apelul a reușit, deci logăm succesul și returnăm răspunsul
                $logData = date('Y-m-d H:i:s') . " - Apel SOAP reușit pentru metoda {$method} la încercarea {$attempt}\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                return $response;
            } catch (SoapFault $e) {
                // Salvăm excepția pentru a o putea arunca dacă toate încercările eșuează
                $lastException = $e;

                // Logăm eroarea
                $logData = date('Y-m-d H:i:s') . " - Eroare la încercarea {$attempt}/{$this->maxRetries} pentru metoda {$method}: " . $e->getMessage() . "\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                // Verificăm dacă eroarea este una care poate fi rezolvată prin reîncercare
                $retryableError = (
                    strpos($e->getMessage(), 'looks like we got no XML document') !== false ||
                    strpos($e->getMessage(), 'Connection reset by peer') !== false ||
                    strpos($e->getMessage(), 'Error Fetching http headers') !== false ||
                    strpos($e->getMessage(), 'Could not connect to host') !== false ||
                    strpos($e->getMessage(), 'Operation timed out') !== false
                );

                if (!$retryableError) {
                    // Dacă eroarea nu este una care poate fi rezolvată prin reîncercare, o aruncăm imediat
                    $logData = date('Y-m-d H:i:s') . " - Eroare nerecuperabilă, nu mai reîncercăm: " . $e->getMessage() . "\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                    throw new Exception("{$errorPrefix}: " . $e->getMessage());
                }

                // Dacă nu am epuizat toate încercările, așteptăm înainte de a reîncerca
                if ($attempt < $this->maxRetries) {
                    // Calculăm timpul de așteptare cu backoff exponențial
                    $waitTime = $this->retryDelay * pow(2, $attempt - 1);

                    $logData = date('Y-m-d H:i:s') . " - Așteptăm {$waitTime} microsecunde înainte de reîncercare\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);

                    // Așteptăm înainte de a reîncerca
                    usleep($waitTime);
                }
            }
        }

        // Dacă am ajuns aici, toate încercările au eșuat
        $logData = date('Y-m-d H:i:s') . " - Toate încercările au eșuat pentru metoda {$method}\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        // Logăm eroarea și în fișierul de erori SOAP
        $errorLogFile = "{$logDir}/search_errors.log";
        $errorLogData = date('Y-m-d H:i:s') . " - Eroare SOAP după {$this->maxRetries} încercări: " . $lastException->getMessage() . "\n";
        file_put_contents($errorLogFile, $errorLogData, FILE_APPEND);

        throw new Exception("{$errorPrefix} (după {$this->maxRetries} încercări): " . $lastException->getMessage());
    }

    /**
     * Caută dosare cu parametri multipli
     *
     * @param array $params Parametrii de căutare
     * @return array Rezultatele căutării
     */
    public function cautareAvansata($params) {
        try {
            // Extragem parametrul maxResults dacă există
            $maxResults = isset($params['_maxResults']) ? (int)$params['_maxResults'] : 1000;
            unset($params['_maxResults']); // Eliminăm parametrul din array pentru a nu-l trimite la API

            // Handle empty institutie parameter - convert empty string to null
            $institutie = isset($params['institutie']) && $params['institutie'] !== '' ? $params['institutie'] : null;

            // Asigurăm-ne că numeParte este corect codificat pentru SOAP
            $numeParte = $params['numeParte'] ?? '';

            // Încercăm atât cu textul original, cât și cu textul normalizat
            $normalizedNumeParte = $this->normalizeDiacritics($numeParte);

            // Creăm un director pentru loguri dacă nu există
            $logDir = __DIR__ . '/../logs';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            // Logăm parametrii de căutare pentru depanare
            $logFile = "{$logDir}/search_params.log";
            $logData = date('Y-m-d H:i:s') . " - Parametri căutare: " . json_encode($params, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($logFile, $logData, FILE_APPEND);

            // Validăm datele pentru a ne asigura că datele de sfârșit nu sunt înainte de datele de început
            $dataStart = $params['dataStart'] ?? '';
            $dataStop = $params['dataStop'] ?? '';
            $dataUltimaModificareStart = $params['dataUltimaModificareStart'] ?? '';
            $dataUltimaModificareStop = $params['dataUltimaModificareStop'] ?? '';

            // Verificăm și corectăm datele dacă este necesar
            if (!empty($dataStart) && !empty($dataStop)) {
                $startTimestamp = strtotime($dataStart);
                $stopTimestamp = strtotime($dataStop);

                if ($startTimestamp && $stopTimestamp && $startTimestamp > $stopTimestamp) {
                    // Dacă data de început este după data de sfârșit, le inversăm
                    $logData = date('Y-m-d H:i:s') . " - Avertisment: Data de început ({$dataStart}) este după data de sfârșit ({$dataStop}). Inversăm datele.\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);

                    $temp = $dataStart;
                    $dataStart = $dataStop;
                    $dataStop = $temp;
                }
            }

            if (!empty($dataUltimaModificareStart) && !empty($dataUltimaModificareStop)) {
                $startTimestamp = strtotime($dataUltimaModificareStart);
                $stopTimestamp = strtotime($dataUltimaModificareStop);

                if ($startTimestamp && $stopTimestamp && $startTimestamp > $stopTimestamp) {
                    // Dacă data de început este după data de sfârșit, le inversăm
                    $logData = date('Y-m-d H:i:s') . " - Avertisment: Data ultimei modificări de început ({$dataUltimaModificareStart}) este după data ultimei modificări de sfârșit ({$dataUltimaModificareStop}). Inversăm datele.\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);

                    $temp = $dataUltimaModificareStart;
                    $dataUltimaModificareStart = $dataUltimaModificareStop;
                    $dataUltimaModificareStop = $temp;
                }
            }

            // Pregătim parametrii pentru căutare
            $searchParams = [
                'numarDosar' => $params['numarDosar'] ?? '',
                'obiectDosar' => $params['obiectDosar'] ?? '',
                'numeParte' => $numeParte, // Folosim versiunea originală
                'institutie' => $institutie,
                'dataStart' => $this->formatDateForSoap($dataStart),
                'dataStop' => $this->formatDateForSoap($dataStop),
                'dataUltimaModificareStart' => $this->formatDateForSoap($dataUltimaModificareStart),
                'dataUltimaModificareStop' => $this->formatDateForSoap($dataUltimaModificareStop)
            ];

            // Logăm parametrii SOAP pentru depanare
            $logData = date('Y-m-d H:i:s') . " - Parametri SOAP: " . json_encode($searchParams, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($logFile, $logData, FILE_APPEND);

            // Facem prima căutare cu parametrii originali folosind mecanismul de reîncercare
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $searchParams, "Eroare la căutarea avansată");

            // Procesăm rezultatele cu limita specificată
            $results = $this->processResponse($response, $maxResults);

            // Logăm răspunsul pentru depanare (doar informații de bază)
            $responseInfo = "Răspuns primit (versiune originală): ";
            if (isset($response->CautareDosare2Result->Dosar)) {
                $dosare = $response->CautareDosare2Result->Dosar;
                if (is_array($dosare)) {
                    $responseInfo .= "Număr dosare găsite: " . count($dosare);
                } else {
                    $responseInfo .= "Un singur dosar găsit";
                }
            } else {
                $responseInfo .= "Niciun dosar găsit";
            }
            $logData = date('Y-m-d H:i:s') . " - " . $responseInfo . "\n";
            file_put_contents($logFile, $logData, FILE_APPEND);

            // Dacă nu am găsit rezultate și avem un nume de parte cu diacritice, încercăm cu versiunea normalizată
            if (empty($results) && !empty($numeParte) && $numeParte !== $normalizedNumeParte) {
                $logData = date('Y-m-d H:i:s') . " - Încercare căutare cu nume normalizat: " . $normalizedNumeParte . "\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                // Creăm parametrii pentru căutarea cu nume normalizat
                $normalizedParams = $searchParams;
                $normalizedParams['numeParte'] = $normalizedNumeParte;

                // Logăm parametrii normalizați
                $logData = date('Y-m-d H:i:s') . " - Parametri SOAP normalizați: " . json_encode($normalizedParams, JSON_UNESCAPED_UNICODE) . "\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                try {
                    // Facem căutarea cu parametrii normalizați folosind mecanismul de reîncercare
                    $normalizedResponse = $this->executeSoapCallWithRetry('CautareDosare2', $normalizedParams, "Eroare la căutarea cu nume normalizat");
                    $normalizedResults = $this->processResponse($normalizedResponse, $maxResults);

                    // Logăm rezultatele căutării normalizate
                    $normalizedResponseInfo = "Răspuns primit (versiune normalizată): ";
                    if (isset($normalizedResponse->CautareDosare2Result->Dosar)) {
                        $dosare = $normalizedResponse->CautareDosare2Result->Dosar;
                        if (is_array($dosare)) {
                            $normalizedResponseInfo .= "Număr dosare găsite: " . count($dosare);
                        } else {
                            $normalizedResponseInfo .= "Un singur dosar găsit";
                        }
                    } else {
                        $normalizedResponseInfo .= "Niciun dosar găsit";
                    }
                    $logData = date('Y-m-d H:i:s') . " - " . $normalizedResponseInfo . "\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);

                    // Dacă am găsit rezultate cu versiunea normalizată, le returnăm pe acestea
                    if (!empty($normalizedResults)) {
                        $logData = date('Y-m-d H:i:s') . " - Returnăm rezultatele găsite cu versiunea normalizată\n";
                        file_put_contents($logFile, $logData, FILE_APPEND);
                        return $normalizedResults;
                    }
                } catch (Exception $e) {
                    $logData = date('Y-m-d H:i:s') . " - Eroare la căutarea cu nume normalizat: " . $e->getMessage() . "\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                }
            }

            // Returnăm rezultatele originale (pot fi goale)
            return $results;
        } catch (Exception $e) {
            // Logăm eroarea pentru depanare
            $logDir = __DIR__ . '/../logs';
            $logFile = "{$logDir}/search_errors.log";
            $logData = date('Y-m-d H:i:s') . " - Eroare: " . $e->getMessage() . "\n";
            file_put_contents($logFile, $logData, FILE_APPEND);

            throw new Exception("Eroare la căutarea avansată: " . $e->getMessage());
        }
    }

    /**
     * Caută un dosar în toate instituțiile disponibile
     *
     * @param string $numarDosar Numărul dosarului
     * @return object|null Detaliile dosarului sau null dacă nu este găsit
     */
    public function searchDosarInAllInstitutions($numarDosar) {
        // Obținem lista tuturor instituțiilor
        $institutii = getInstanteList();

        // Încercăm să găsim dosarul în fiecare instituție
        foreach ($institutii as $codInstitutie => $numeInstitutie) {
            try {
                $dosar = $this->getDetaliiDosar($numarDosar, $codInstitutie);
                if ($dosar && !empty($dosar->numar)) {
                    return $dosar;
                }
            } catch (Exception $e) {
                // Continuăm căutarea în următoarea instituție
                continue;
            }
        }

        return null;
    }

    /**
     * Obține detalii pentru un dosar specific cu informații de debug
     *
     * @param string $numarDosar Numărul dosarului
     * @param string $institutie Instituția
     * @param bool $debug Dacă să returneze informații de debug
     * @return object|array Detaliile dosarului sau array cu detalii și debug info
     */
    public function getDetaliiDosarWithDebug($numarDosar, $institutie, $debug = false) {
        try {
            // Ensure institutie is not empty
            if (empty($institutie)) {
                throw new Exception("Instituția este obligatorie pentru obținerea detaliilor dosarului.");
            }

            // Parametrii pentru căutare
            $searchParams = [
                'numarDosar' => $numarDosar,
                'institutie' => $institutie,
                'obiectDosar' => '',
                'numeParte' => '',
                'dataStart' => null,
                'dataStop' => null,
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];

            // Apelare metodă SOAP cu mecanism de reîncercare
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $searchParams, "Eroare la obținerea detaliilor dosarului");

            $debugInfo = [];
            if ($debug) {
                $debugInfo['raw_response'] = $response;
                $debugInfo['search_params'] = $searchParams;
            }

            // Procesare rezultat
            if (isset($response->CautareDosare2Result->Dosar)) {
                $dosare = $response->CautareDosare2Result->Dosar;

                if ($debug) {
                    $debugInfo['dosare_count'] = is_array($dosare) ? count($dosare) : 1;
                    $debugInfo['dosare_structure'] = $dosare;
                }

                // Verifică dacă rezultatul este un singur dosar sau un array de dosare
                if (!is_array($dosare)) {
                    $mappedDosar = $this->mapDosarToObject($dosare);
                    if ($debug) {
                        $debugInfo['raw_dosar'] = $dosare;
                        $debugInfo['mapped_dosar'] = $mappedDosar;
                        return ['dosar' => $mappedDosar, 'debug' => $debugInfo];
                    }
                    return $mappedDosar;
                } else {
                    // Caută dosarul specific în rezultate
                    foreach ($dosare as $dosar) {
                        if ($dosar->numar === $numarDosar && $dosar->institutie === $institutie) {
                            $mappedDosar = $this->mapDosarToObject($dosar);
                            if ($debug) {
                                $debugInfo['raw_dosar'] = $dosar;
                                $debugInfo['mapped_dosar'] = $mappedDosar;
                                return ['dosar' => $mappedDosar, 'debug' => $debugInfo];
                            }
                            return $mappedDosar;
                        }
                    }
                }
            }

            if ($debug) {
                $debugInfo['error'] = 'No matching case found';
                return ['dosar' => (object)[], 'debug' => $debugInfo];
            }

            // Returnăm un obiect gol în loc de null pentru a respecta tipul de returnare
            return (object)[];
        } catch (Exception $e) {
            if ($debug) {
                return ['dosar' => (object)[], 'debug' => ['error' => $e->getMessage()]];
            }
            throw new Exception("Eroare la obținerea detaliilor dosarului: " . $e->getMessage());
        }
    }

    /**
     * Obține detalii pentru un dosar specific
     *
     * @param string $numarDosar Numărul dosarului
     * @param string $institutie Instituția
     * @return object Detaliile dosarului
     */
    public function getDetaliiDosar($numarDosar, $institutie) {
        try {
            // Ensure institutie is not empty
            if (empty($institutie)) {
                throw new Exception("Instituția este obligatorie pentru obținerea detaliilor dosarului.");
            }

            // Parametrii pentru căutare
            $searchParams = [
                'numarDosar' => $numarDosar,
                'institutie' => $institutie,
                'obiectDosar' => '',
                'numeParte' => '',
                'dataStart' => null,
                'dataStop' => null,
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];

            // Apelare metodă SOAP cu mecanism de reîncercare
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $searchParams, "Eroare la obținerea detaliilor dosarului");

            // Procesare rezultat
            if (isset($response->CautareDosare2Result->Dosar)) {
                $dosare = $response->CautareDosare2Result->Dosar;

                // Verifică dacă rezultatul este un singur dosar sau un array de dosare
                if (!is_array($dosare)) {
                    return $this->mapDosarToObject($dosare);
                } else {
                    // Caută dosarul specific în rezultate
                    foreach ($dosare as $dosar) {
                        if ($dosar->numar === $numarDosar && $dosar->institutie === $institutie) {
                            return $this->mapDosarToObject($dosar);
                        }
                    }
                }
            }

            // Returnăm un obiect gol în loc de null pentru a respecta tipul de returnare
            return (object)[];
        } catch (Exception $e) {
            throw new Exception("Eroare la obținerea detaliilor dosarului: " . $e->getMessage());
        }
    }

    /**
     * Procesează răspunsul de la API pentru căutare
     *
     * @param object $response Răspunsul de la API
     * @param int $maxResults Numărul maxim de rezultate de procesat
     * @return array Rezultatele procesate
     */
    private function processResponse($response, $maxResults = 1000) {
        $results = [];
        $count = 0;

        if (isset($response->CautareDosare2Result->Dosar)) {
            $dosare = $response->CautareDosare2Result->Dosar;

            // Verificăm dacă avem un singur dosar sau mai multe
            if (is_array($dosare)) {
                foreach ($dosare as $dosar) {
                    // Verificăm dacă am atins limita maximă de rezultate
                    if ($count >= $maxResults) {
                        break;
                    }

                    $results[] = $this->mapDosarToObject($dosar);
                    $count++;
                }
            } else {
                // Pentru un singur dosar, îl adăugăm direct
                $results[] = $this->mapDosarToObject($dosare);
            }
        }

        return $results;
    }

    /**
     * Mapează un dosar din răspunsul API la un obiect
     *
     * @param object $dosar Dosarul din răspunsul API
     * @return object Obiectul mapat
     */
    private function mapDosarToObject($dosar) {
        $obj = new stdClass();

        // Informații de bază despre dosar
        $obj->numar = $dosar->numar ?? '';
        $obj->numarVechi = $dosar->numarVechi ?? '';
        $obj->data = isset($dosar->data) ? $this->formatDateFromSoap($dosar->data) : '';
        $obj->institutie = $dosar->institutie ?? '';
        $obj->departament = $dosar->departament ?? '';
        $obj->categorieCaz = $dosar->categorieCaz ?? '';
        $obj->categorieCazNume = $dosar->categorieCazNume ?? '';
        $obj->stadiuProcesual = $dosar->stadiuProcesual ?? '';
        $obj->stadiuProcesualNume = $dosar->stadiuProcesualNume ?? '';
        $obj->obiect = $dosar->obiect ?? '';
        $obj->dataModificare = isset($dosar->dataModificare) ? $this->formatDateFromSoap($dosar->dataModificare) : '';

        // Părțile implicate - Extragere hibridă (SOAP API + text decizie)
        $obj->parti = [];

        // 1. Extrage părțile din SOAP API (primele ~100)
        $soapParties = [];
        if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
            $parti = $dosar->parti->DosarParte;
            if (is_array($parti)) {
                foreach ($parti as $parte) {
                    if (isset($parte->nume)) {
                        $soapParties[] = [
                            'nume' => $parte->nume ?? '',
                            'calitate' => $parte->calitateParte ?? '',
                            'source' => 'soap_api'
                        ];
                    }
                }
            } elseif (isset($parti->nume)) {
                $soapParties[] = [
                    'nume' => $parti->nume ?? '',
                    'calitate' => $parti->calitateParte ?? '',
                    'source' => 'soap_api'
                ];
            }
        }

        // 2. Extrage părțile suplimentare din textul deciziei (pentru a depăși limita de 100)
        $decisionParties = $this->extractPartiesFromDecisionText($dosar);

        // 3. Combină și deduplică părțile
        $obj->parti = $this->mergeAndDeduplicateParties($soapParties, $decisionParties);

        // 4. Log pentru debugging
        if (count($soapParties) >= 100 && count($obj->parti) > count($soapParties)) {
            error_log("Enhanced party extraction: SOAP=" . count($soapParties) . ", Decision=" . count($decisionParties) . ", Total=" . count($obj->parti));
        }

        // Ședințele de judecată
        $obj->sedinte = [];
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (is_array($sedinte)) {
                foreach ($sedinte as $sedinta) {
                    if (isset($sedinta->data)) {
                        $obj->sedinte[] = [
                            'data' => isset($sedinta->data) ? $this->formatDateFromSoap($sedinta->data) : '',
                            'ora' => $sedinta->ora ?? '',
                            'complet' => $sedinta->complet ?? '',
                            'solutie' => $sedinta->solutie ?? '',
                            'solutieSumar' => $sedinta->solutieSumar ?? '',
                            'dataPronuntare' => isset($sedinta->dataPronuntare) ? $this->formatDateFromSoap($sedinta->dataPronuntare) : '',
                            'documentSedinta' => $sedinta->documentSedinta ?? '',
                            'numarDocument' => $sedinta->numarDocument ?? '',
                            'dataDocument' => isset($sedinta->dataDocument) ? $this->formatDateFromSoap($sedinta->dataDocument) : ''
                        ];
                    }
                }
            } elseif (isset($dosar->sedinte->DosarSedinta->data)) {
                $sedinta = $dosar->sedinte->DosarSedinta;
                $obj->sedinte[] = [
                    'data' => isset($sedinta->data) ? $this->formatDateFromSoap($sedinta->data) : '',
                    'ora' => $sedinta->ora ?? '',
                    'complet' => $sedinta->complet ?? '',
                    'solutie' => $sedinta->solutie ?? '',
                    'solutieSumar' => $sedinta->solutieSumar ?? '',
                    'dataPronuntare' => isset($sedinta->dataPronuntare) ? $this->formatDateFromSoap($sedinta->dataPronuntare) : '',
                    'documentSedinta' => $sedinta->documentSedinta ?? '',
                    'numarDocument' => $sedinta->numarDocument ?? '',
                    'dataDocument' => isset($sedinta->dataDocument) ? $this->formatDateFromSoap($sedinta->dataDocument) : ''
                ];
            }
        }

        // Căile de atac
        $obj->caiAtac = [];
        if (isset($dosar->caiAtac) && isset($dosar->caiAtac->DosarCaleAtac)) {
            $caiAtac = $dosar->caiAtac->DosarCaleAtac;
            if (is_array($caiAtac)) {
                foreach ($caiAtac as $caleAtac) {
                    if (isset($caleAtac->dataDeclarare)) {
                        $obj->caiAtac[] = [
                            'dataDeclarare' => isset($caleAtac->dataDeclarare) ? $this->formatDateFromSoap($caleAtac->dataDeclarare) : '',
                            'tipCaleAtac' => $caleAtac->tipCaleAtac ?? '',
                            'parteDeclaratoare' => $caleAtac->parteDeclaratoare ?? '',
                            'numarDosarInstantaSuperior' => $caleAtac->numarDosarInstantaSuperior ?? '',
                            'instantaSuperior' => $caleAtac->instantaSuperior ?? ''
                        ];
                    }
                }
            } elseif (isset($dosar->caiAtac->DosarCaleAtac->dataDeclarare)) {
                $caleAtac = $dosar->caiAtac->DosarCaleAtac;
                $obj->caiAtac[] = [
                    'dataDeclarare' => isset($caleAtac->dataDeclarare) ? $this->formatDateFromSoap($caleAtac->dataDeclarare) : '',
                    'tipCaleAtac' => $caleAtac->tipCaleAtac ?? '',
                    'parteDeclaratoare' => $caleAtac->parteDeclaratoare ?? '',
                    'numarDosarInstantaSuperior' => $caleAtac->numarDosarInstantaSuperior ?? '',
                    'instantaSuperior' => $caleAtac->instantaSuperior ?? ''
                ];
            }
        }

        return $obj;
    }

    /**
     * Extrage părțile din textul deciziei judecătorești pentru a depăși limita SOAP API
     *
     * @param object $dosar Obiectul dosar din răspunsul SOAP
     * @return array Lista părților extrase din textul deciziei
     */
    private function extractPartiesFromDecisionText($dosar)
    {
        $decisionParties = [];

        // Caută în ședințele de judecată pentru textul deciziei
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }

            foreach ($sedinte as $sedinta) {
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $solutieText = $sedinta->solutieSumar;

                    // Extract parties with contextual quality detection
                    $extractedParties = $this->extractPartiesWithQuality($solutieText);
                    $decisionParties = array_merge($decisionParties, $extractedParties);
                }
            }
        }

        // Log pentru debugging
        error_log("Extracted " . count($decisionParties) . " parties from decision text with enhanced quality detection");

        return $decisionParties;
    }

    /**
     * Extract parties with quality detection from decision text
     * Uses multiple patterns to identify parties and their qualities from context
     *
     * @param string $solutieText The decision text to parse
     * @return array Array of parties with detected qualities
     */
    private function extractPartiesWithQuality($solutieText)
    {
        $parties = [];

        // Pattern 1: "formulate de creditorii" - these are creditors
        if (preg_match('/formulate de creditorii ([^;]+(?:;[^;]+)*)/i', $solutieText, $matches)) {
            $partiesText = $matches[1];
            $partiesText = preg_replace('/\.\s*Suma de 200 de lei.*$/s', '', $partiesText);
            $partyNames = explode(';', $partiesText);

            foreach ($partyNames as $partyName) {
                $partyName = trim($partyName);
                $partyName = preg_replace('/\s*\(date\)\s*$/', '', $partyName);
                $partyName = preg_replace('/^\s*și\s+/', '', $partyName);
                $partyName = trim($partyName);

                if ($this->isValidPartyNameLegacy($partyName)) {
                    $parties[] = [
                        'nume' => $partyName,
                        'calitate' => 'Creditor',
                        'source' => 'decision_text'
                    ];
                }
            }
        }

        // Pattern 2: "intervenienţi în interesul debitorului" - these are interveners
        if (preg_match('/introducerea acestora în cauză[^.]*în calitate de intervenienţi[^.]*\./i', $solutieText, $matches)) {
            // Find the party list that precedes this statement
            $beforeText = substr($solutieText, 0, strpos($solutieText, $matches[0]));

            // Look for party names before "Dispune" - simplified pattern
            // This pattern looks for the last sentence that contains party names
            if (preg_match('/([A-ZĂÂÎȘȚŢ][^.]*)\s*\.\s*Dispune/u', $beforeText, $partyMatches)) {
                $partiesText = $partyMatches[1];

                // Clean up the text - remove any legal boilerplate that might have been captured
                $partiesText = preg_replace('/^.*formulate de intervenienţii\s*/i', '', $partiesText);
                $partiesText = preg_replace('/–[^,]*;[^,]*/u', '', $partiesText); // Remove legal representation info
                $partiesText = preg_replace('/prin mandatar[^,]*;[^,]*/u', '', $partiesText); // Remove mandatar info

                $partyNames = explode(',', $partiesText);

                foreach ($partyNames as $partyName) {
                    $partyName = trim($partyName);
                    // Additional cleanup for individual names
                    $partyName = preg_replace('/–.*$/', '', $partyName); // Remove anything after dash
                    $partyName = preg_replace('/prin mandatar.*$/', '', $partyName); // Remove mandatar info
                    $partyName = trim($partyName);

                    if ($this->isValidPartyNameLegacy($partyName)) {
                        $parties[] = [
                            'nume' => $partyName,
                            'calitate' => 'Intervenient în numele altei persoane',
                            'source' => 'decision_text'
                        ];
                    }
                }
            }
        }

        return $parties;
    }

    /**
     * Validate if a party name meets quality criteria (legacy version)
     *
     * @param string $partyName The party name to validate
     * @return bool True if valid, false otherwise
     */
    private function isValidPartyNameLegacy($partyName)
    {
        // Length validation
        if (strlen($partyName) < 2 || strlen($partyName) > 100) {
            return false;
        }

        // Enhanced Romanian diacritics pattern including Ţ/ţ (cedilla) variants
        if (!preg_match('/^[A-ZĂÂÎȘȚŢ][A-ZĂÂÎȘȚŢ\s\-\.\(\)]+$/u', $partyName)) {
            return false;
        }

        // Blacklist filtering - exclude legal terminology
        if (preg_match('/\b(suma|lei|reprezentând|ajutor|public|judiciar|conform|pronunţată|dosarul|rămâne|sarcina|statului|drept|apel|termen|zile|comunicare|depune|tribunalul|secţia|civilă|punerea|soluţiei|dispoziţia|părţilor|mijlocirea|grefei|instanţei|astăzi|camera|consiliu|acestora)\b/i', $partyName)) {
            return false;
        }

        return true;
    }

    /**
     * Combină și deduplică părțile din SOAP API și textul deciziei
     *
     * @param array $soapParties Părțile din SOAP API
     * @param array $decisionParties Părțile din textul deciziei
     * @return array Lista finală de părți deduplicate
     */
    private function mergeAndDeduplicateParties($soapParties, $decisionParties)
    {
        $mergedParties = [];
        $seenNames = [];

        // Adaugă părțile din SOAP API (prioritate mai mare)
        foreach ($soapParties as $party) {
            $normalizedName = $this->normalizePartyName($party['nume']);
            if (!isset($seenNames[$normalizedName])) {
                $mergedParties[] = [
                    'nume' => $party['nume'],
                    'calitate' => $party['calitate'],
                    'source' => $party['source'] ?? 'soap_api'
                ];
                $seenNames[$normalizedName] = [
                    'index' => count($mergedParties) - 1,
                    'quality' => $party['calitate'],
                    'source' => $party['source'] ?? 'soap_api'
                ];
            }
        }

        // Adaugă părțile din textul deciziei cu prioritizarea calității
        foreach ($decisionParties as $party) {
            $normalizedName = $this->normalizePartyName($party['nume']);

            if (!isset($seenNames[$normalizedName])) {
                // Parte nouă - o adaugă
                $mergedParties[] = [
                    'nume' => $party['nume'],
                    'calitate' => $party['calitate'],
                    'source' => $party['source'] ?? 'decision_text'
                ];
                $seenNames[$normalizedName] = [
                    'index' => count($mergedParties) - 1,
                    'quality' => $party['calitate'],
                    'source' => $party['source'] ?? 'decision_text'
                ];
            } else {
                // Partea există deja - verifică dacă trebuie să actualizeze calitatea
                $existingQuality = $seenNames[$normalizedName]['quality'];
                $newQuality = $party['calitate'];

                // Prioritizează calitățile mai specifice față de cele generice
                if ($this->shouldUpdateQualityLegacy($existingQuality, $newQuality)) {
                    $index = $seenNames[$normalizedName]['index'];
                    $mergedParties[$index]['calitate'] = $newQuality;
                    $seenNames[$normalizedName]['quality'] = $newQuality;
                    // Note: Keep original source when updating quality
                }
            }
        }

        return $mergedParties;
    }

    /**
     * Determină dacă calitatea unei părți trebuie actualizată pe baza priorității
     *
     * @param string $existingQuality Calitatea curentă
     * @param string $newQuality Calitatea nouă de considerat
     * @return bool True dacă calitatea trebuie actualizată
     */
    private function shouldUpdateQualityLegacy($existingQuality, $newQuality)
    {
        // Ierarhia priorității calității (număr mai mare = prioritate mai mare)
        $qualityPriority = [
            'Creditor' => 1,  // Calitate generică
            'Debitor' => 2,
            'Pârât' => 3,
            'Reclamant' => 3,
            'Appelant' => 3,
            'Intimat' => 3,
            'Intervenient în numele altei persoane' => 4  // Cea mai specifică
        ];

        $existingPriority = $qualityPriority[$existingQuality] ?? 0;
        $newPriority = $qualityPriority[$newQuality] ?? 0;

        return $newPriority > $existingPriority;
    }

    /**
     * Normalizează numele unei părți pentru comparație și deduplicare
     *
     * @param string $name Numele părții
     * @return string Numele normalizat
     */
    private function normalizePartyName($name)
    {
        // Convertește la minuscule și elimină diacriticele
        $normalized = strtolower($this->normalizeDiacritics($name));

        // Elimină spațiile multiple și caracterele speciale
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        $normalized = preg_replace('/[^\w\s\-]/', '', $normalized);
        $normalized = trim($normalized);

        return $normalized;
    }

    /**
     * Formatează o dată pentru a fi utilizată în cererea SOAP
     * Funcție îmbunătățită pentru a gestiona mai multe formate de dată
     *
     * @param string $date Data în format string (d.m.Y sau alte formate)
     * @return string Data formatată pentru SOAP sau null dacă data este invalidă
     */
    private function formatDateForSoap($date) {
        if (empty($date)) {
            return null;
        }

        // Creăm un director pentru loguri dacă nu există
        $logDir = __DIR__ . '/../logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // Logăm data originală pentru depanare
        $logFile = "{$logDir}/date_format_debug.log";
        $logData = date('Y-m-d H:i:s') . " - Data originală: {$date}\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        // Încercăm mai multe formate posibile
        $formats = [
            'd.m.Y',    // 31.12.2023
            'Y-m-d',    // 2023-12-31
            'd/m/Y',    // 31/12/2023
            'Y/m/d',    // 2023/12/31
            'd-m-Y',    // 31-12-2023
            'j.n.Y',    // 1.1.2023 (fără zero-uri)
            'j/n/Y',    // 1/1/2023 (fără zero-uri)
            'j-n-Y'     // 1-1-2023 (fără zero-uri)
        ];

        foreach ($formats as $format) {
            $dateObj = DateTime::createFromFormat($format, $date);
            if ($dateObj && $dateObj->format($format) == $date) {
                $formattedDate = $dateObj->format('Y-m-d\TH:i:s');
                $logData = date('Y-m-d H:i:s') . " - Data formatată pentru SOAP: {$formattedDate} (format detectat: {$format})\n";
                file_put_contents($logFile, $logData, FILE_APPEND);
                return $formattedDate;
            }
        }

        // Încercăm să parsăm data cu strtotime ca ultimă soluție
        $timestamp = strtotime($date);
        if ($timestamp !== false) {
            $formattedDate = date('Y-m-d\TH:i:s', $timestamp);
            $logData = date('Y-m-d H:i:s') . " - Data formatată pentru SOAP: {$formattedDate} (folosind strtotime)\n";
            file_put_contents($logFile, $logData, FILE_APPEND);
            return $formattedDate;
        }

        // Dacă nu am reușit să parsăm data, logăm eroarea
        $logData = date('Y-m-d H:i:s') . " - Eroare: Nu s-a putut formata data '{$date}' pentru SOAP\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        return null;
    }

    /**
     * Formatează o dată primită de la SOAP
     *
     * @param string $date Data în format SOAP
     * @return string Data formatată (d.m.Y)
     */
    private function formatDateFromSoap($date) {
        if (empty($date)) {
            return '';
        }

        try {
            $dateObj = new DateTime($date);
            return $dateObj->format('d.m.Y');
        } catch (Exception $e) {
            return '';
        }
    }

    /**
     * Normalizează caracterele diacritice pentru a asigura compatibilitatea cu API-ul SOAP
     * Funcție îmbunătățită pentru a gestiona toate variațiile posibile de diacritice românești
     *
     * @param string $text Textul care trebuie normalizat
     * @return string Textul normalizat
     */
    private function normalizeDiacritics($text) {
        if (empty($text)) {
            return '';
        }

        // Creăm un director pentru loguri dacă nu există
        $logDir = __DIR__ . '/../logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // Logăm textul original pentru depanare
        $logFile = $logDir . '/diacritics_debug.log';
        $logData = date('Y-m-d H:i:s') . " - Text original: " . $text . "\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        // Mapare extinsă a caracterelor diacritice la forma lor normalizată
        // Include toate variațiile posibile de codificare pentru diacriticele românești
        $diacritics = [
            // Diacritice românești standard
            'ă' => 'a', 'Ă' => 'A',
            'â' => 'a', 'Â' => 'A',
            'î' => 'i', 'Î' => 'I',
            'ș' => 's', 'Ș' => 'S',
            'ț' => 't', 'Ț' => 'T',

            // Variante alternative de codificare
            'ş' => 's', 'Ş' => 'S',
            'ţ' => 't', 'Ţ' => 'T',

            // Variante cu accente
            'á' => 'a', 'Á' => 'A',
            'à' => 'a', 'À' => 'A',
            'ä' => 'a', 'Ä' => 'A',
            'é' => 'e', 'É' => 'E',
            'è' => 'e', 'È' => 'E',
            'ë' => 'e', 'Ë' => 'E',
            'í' => 'i', 'Í' => 'I',
            'ì' => 'i', 'Ì' => 'I',
            'ï' => 'i', 'Ï' => 'I',
            'ó' => 'o', 'Ó' => 'O',
            'ò' => 'o', 'Ò' => 'O',
            'ö' => 'o', 'Ö' => 'O',
            'ú' => 'u', 'Ú' => 'U',
            'ù' => 'u', 'Ù' => 'U',
            'ü' => 'u', 'Ü' => 'U'
        ];

        // Metoda 1: Utilizăm strtr pentru înlocuire directă
        $normalizedText = strtr($text, $diacritics);

        // Metoda 2: Utilizăm transliterarea iconv ca backup
        // Această metodă poate gestiona și alte caractere Unicode care nu sunt în maparea noastră
        if (function_exists('iconv')) {
            $translit = @iconv('UTF-8', 'ASCII//TRANSLIT', $text);
            if ($translit !== false) {
                // Dacă transliterarea a reușit, comparăm rezultatele și alegem cel mai bun
                // Preferăm rezultatul strtr dacă diferă doar prin diacritice
                if (strlen($normalizedText) !== strlen($text) || $normalizedText === $text) {
                    $normalizedText = $translit;
                }
            }
        }

        // Metoda 3: Utilizăm Normalizer din intl dacă este disponibil
        if (class_exists('Normalizer')) {
            // Descompunem caracterele în forma lor de bază + accente
            $decomposed = \Normalizer::normalize($text, \Normalizer::FORM_D);
            if ($decomposed !== false) {
                // Eliminăm toate semnele diacritice (categoria Mn - Mark, nonspacing)
                $withoutDiacritics = preg_replace('/\p{Mn}/u', '', $decomposed);
                if ($withoutDiacritics !== null) {
                    // Dacă rezultatul este mai bun decât cel obținut anterior, îl folosim
                    if (strlen($normalizedText) !== strlen($text) || $normalizedText === $text) {
                        $normalizedText = $withoutDiacritics;
                    }
                }
            }
        }

        // Logăm textul normalizat pentru depanare
        $logData = date('Y-m-d H:i:s') . " - Text normalizat: " . $normalizedText . "\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        return $normalizedText;
    }
}