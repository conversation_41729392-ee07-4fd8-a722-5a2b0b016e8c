<?php
/**
 * Test script to verify parties display issue
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

try {
    $dosarService = new DosarService();
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if ($dosar && !empty($dosar->parti)) {
        echo "Total parties in backend: " . count($dosar->parti) . PHP_EOL;
        echo "First 5 parties:" . PHP_EOL;
        for ($i = 0; $i < min(5, count($dosar->parti)); $i++) {
            echo ($i + 1) . ". " . $dosar->parti[$i]['nume'] . PHP_EOL;
        }
        echo "..." . PHP_EOL;
        echo "Last 5 parties:" . PHP_EOL;
        $start = max(0, count($dosar->parti) - 5);
        for ($i = $start; $i < count($dosar->parti); $i++) {
            echo ($i + 1) . ". " . $dosar->parti[$i]['nume'] . PHP_EOL;
        }
        
        // Test if all parties have required data
        $validParties = 0;
        $invalidParties = 0;
        foreach ($dosar->parti as $index => $parte) {
            if (!empty($parte['nume'])) {
                $validParties++;
            } else {
                $invalidParties++;
                echo "Invalid party at index $index: " . print_r($parte, true) . PHP_EOL;
            }
        }
        
        echo "Valid parties: $validParties" . PHP_EOL;
        echo "Invalid parties: $invalidParties" . PHP_EOL;
        
    } else {
        echo "No parties found or dosar not found" . PHP_EOL;
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
?>
