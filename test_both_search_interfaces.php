<?php
/**
 * Test Both Search Interfaces
 * Test both legacy and PSR-4 search interfaces to identify discrepancies
 */

echo "🔍 TESTING BOTH SEARCH INTERFACES\n";
echo "=================================\n\n";

$searchTerm = 'SARAGEA TUDORIŢA';
$expectedCase = '130/98/2022';
$expectedInstitution = 'TribunalulIALOMITA';

echo "🔎 Testing search for: '$searchTerm'\n";
echo "Expected to find: $expectedCase from $expectedInstitution\n";
echo "=" . str_repeat("=", 60) . "\n";

// Test 1: Legacy DosarService
echo "🔧 TEST 1: Legacy DosarService (services/DosarService.php)\n";
try {
    require_once 'config/config.php';
    require_once 'services/DosarService.php';
    
    $legacyDosarService = new DosarService();
    
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => $searchTerm,
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => ''
    ];
    
    $legacyResults = $legacyDosarService->cautareAvansata($searchParams);
    echo "✅ Legacy search completed\n";
    echo "📊 Legacy results: " . count($legacyResults) . "\n";
    
    $legacyFoundExpectedCase = false;
    foreach ($legacyResults as $dosar) {
        echo "  - " . $dosar->numar . " from " . $dosar->institutie . "\n";
        if ($dosar->numar === $expectedCase && $dosar->institutie === $expectedInstitution) {
            $legacyFoundExpectedCase = true;
            echo "    ✅ EXPECTED CASE FOUND!\n";
        }
    }
    
    echo "Legacy expected case found: " . ($legacyFoundExpectedCase ? '✅ YES' : '❌ NO') . "\n\n";
    
} catch (Exception $e) {
    echo "❌ Legacy test error: " . $e->getMessage() . "\n\n";
}

// Test 2: PSR-4 DosarService
echo "🔧 TEST 2: PSR-4 DosarService (src/Services/DosarService.php)\n";
try {
    require_once 'bootstrap.php';
    
    $psr4DosarService = new \App\Services\DosarService();
    
    $psr4Results = $psr4DosarService->cautareAvansata($searchParams);
    echo "✅ PSR-4 search completed\n";
    echo "📊 PSR-4 results: " . count($psr4Results) . "\n";
    
    $psr4FoundExpectedCase = false;
    foreach ($psr4Results as $dosar) {
        echo "  - " . $dosar->numar . " from " . $dosar->institutie . "\n";
        if ($dosar->numar === $expectedCase && $dosar->institutie === $expectedInstitution) {
            $psr4FoundExpectedCase = true;
            echo "    ✅ EXPECTED CASE FOUND!\n";
        }
    }
    
    echo "PSR-4 expected case found: " . ($psr4FoundExpectedCase ? '✅ YES' : '❌ NO') . "\n\n";
    
} catch (Exception $e) {
    echo "❌ PSR-4 test error: " . $e->getMessage() . "\n\n";
}

// Test 3: Compare results
echo "🔧 TEST 3: Comparing results between legacy and PSR-4\n";
if (isset($legacyResults) && isset($psr4Results)) {
    echo "Results count comparison:\n";
    echo "  - Legacy: " . count($legacyResults) . "\n";
    echo "  - PSR-4: " . count($psr4Results) . "\n";
    echo "  - Match: " . (count($legacyResults) === count($psr4Results) ? '✅ YES' : '❌ NO') . "\n";
    
    echo "\nExpected case comparison:\n";
    echo "  - Legacy found: " . ($legacyFoundExpectedCase ? '✅ YES' : '❌ NO') . "\n";
    echo "  - PSR-4 found: " . ($psr4FoundExpectedCase ? '✅ YES' : '❌ NO') . "\n";
    echo "  - Match: " . ($legacyFoundExpectedCase === $psr4FoundExpectedCase ? '✅ YES' : '❌ NO') . "\n";
    
    if (!$legacyFoundExpectedCase || !$psr4FoundExpectedCase) {
        echo "\n❌ ISSUE DETECTED: Expected case missing from one or both implementations\n";
    } else {
        echo "\n✅ SUCCESS: Both implementations find the expected case\n";
    }
} else {
    echo "❌ Cannot compare - one or both tests failed\n";
}

// Test 4: Test filtering method directly
echo "\n🔧 TEST 4: Testing filtering method directly\n";
try {
    // Test the filtering method that was recently implemented
    $reflection = new ReflectionClass($legacyDosarService);
    $filterMethod = $reflection->getMethod('filterPartySearchResults');
    $filterMethod->setAccessible(true);
    
    // Get raw SOAP results first
    $soapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $soapMethod->setAccessible(true);
    
    $soapParams = [
        'numarDosar' => '',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => $searchTerm,
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $rawResponse = $soapMethod->invoke($legacyDosarService, 'CautareDosare2', $soapParams, "Test filtering");
    
    $processMethod = $reflection->getMethod('processResponse');
    $processMethod->setAccessible(true);
    $rawResults = $processMethod->invoke($legacyDosarService, $rawResponse, 1000);
    
    echo "Raw SOAP results: " . count($rawResults) . "\n";
    
    // Apply filtering
    $filteredResults = $filterMethod->invoke($legacyDosarService, $rawResults, $searchTerm);
    echo "Filtered results: " . count($filteredResults) . "\n";
    
    $filterFoundExpectedCase = false;
    foreach ($filteredResults as $dosar) {
        if ($dosar->numar === $expectedCase && $dosar->institutie === $expectedInstitution) {
            $filterFoundExpectedCase = true;
            break;
        }
    }
    
    echo "Expected case found after filtering: " . ($filterFoundExpectedCase ? '✅ YES' : '❌ NO') . "\n";
    
    // Check if expected case exists in raw results
    $rawFoundExpectedCase = false;
    foreach ($rawResults as $dosar) {
        if ($dosar->numar === $expectedCase && $dosar->institutie === $expectedInstitution) {
            $rawFoundExpectedCase = true;
            break;
        }
    }
    
    echo "Expected case found in raw results: " . ($rawFoundExpectedCase ? '✅ YES' : '❌ NO') . "\n";
    
    if ($rawFoundExpectedCase && !$filterFoundExpectedCase) {
        echo "❌ FILTERING BUG: Case exists in raw results but filtered out incorrectly\n";
    } elseif (!$rawFoundExpectedCase) {
        echo "ℹ️  Case not in raw SOAP results - this is expected if SOAP API doesn't return it\n";
    } else {
        echo "✅ Filtering working correctly\n";
    }
    
} catch (Exception $e) {
    echo "❌ Filtering test error: " . $e->getMessage() . "\n";
}

echo "\n🏁 Both search interfaces test completed.\n";
?>
