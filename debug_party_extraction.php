<?php
/**
 * Debug party extraction to see what's happening with specific parties
 */

require_once 'config/config.php';
require_once 'services/DosarService.php';

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "=== DEBUGGING PARTY EXTRACTION ===" . PHP_EOL;

try {
    $dosarService = new DosarService();
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$dosar) {
        echo "❌ ERROR: Could not retrieve case details" . PHP_EOL;
        exit;
    }
    
    echo "Total parties found: " . count($dosar->parti) . PHP_EOL;
    echo PHP_EOL;
    
    // Search for specific parties
    $searchParties = ['SARAGEA TUDORIŢA', 'ZAMFIR NICOLETA'];
    
    foreach ($searchParties as $searchParty) {
        echo "=== SEARCHING FOR: $searchParty ===" . PHP_EOL;
        
        $found = false;
        $similarParties = [];
        
        foreach ($dosar->parti as $index => $parte) {
            $nume = $parte['nume'];
            
            // Exact match
            if ($nume === $searchParty) {
                echo "✅ EXACT MATCH found at index $index: $nume" . PHP_EOL;
                $found = true;
                break;
            }
            
            // Case insensitive match
            if (strcasecmp($nume, $searchParty) === 0) {
                echo "✅ CASE INSENSITIVE MATCH found at index $index: $nume" . PHP_EOL;
                $found = true;
                break;
            }
            
            // Partial match
            if (stripos($nume, $searchParty) !== false || stripos($searchParty, $nume) !== false) {
                $similarParties[] = "Index $index: $nume";
            }
            
            // Similar name (Levenshtein distance)
            $distance = levenshtein(strtolower($nume), strtolower($searchParty));
            if ($distance <= 3 && $distance > 0) {
                $similarParties[] = "Index $index: $nume (distance: $distance)";
            }
        }
        
        if (!$found) {
            echo "❌ NOT FOUND" . PHP_EOL;
            if (!empty($similarParties)) {
                echo "Similar parties found:" . PHP_EOL;
                foreach ($similarParties as $similar) {
                    echo "  - $similar" . PHP_EOL;
                }
            } else {
                echo "No similar parties found" . PHP_EOL;
            }
        }
        
        echo PHP_EOL;
    }
    
    // Show last 20 parties to see what's at the end
    echo "=== LAST 20 PARTIES ===" . PHP_EOL;
    $start = max(0, count($dosar->parti) - 20);
    for ($i = $start; $i < count($dosar->parti); $i++) {
        $parte = $dosar->parti[$i];
        echo ($i + 1) . ". " . $parte['nume'] . " (" . $parte['calitate'] . ")" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "=== DEBUG COMPLETE ===" . PHP_EOL;
?>
