<?php
/**
 * Universal Hybrid Extraction Audit
 * Comprehensive analysis of hybrid party extraction across different cases and institutions
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Universal Hybrid Extraction Audit</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.audit-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 14px; }
.audit-table th, .audit-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
.audit-table th { background-color: #f8f9fa; font-weight: bold; }
.status-success { color: #28a745; font-weight: bold; }
.status-warning { color: #ffc107; font-weight: bold; }
.status-error { color: #dc3545; font-weight: bold; }
.test-link { display: inline-block; margin: 2px; padding: 4px 8px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; font-size: 12px; }
.test-link:hover { background: #0056b3; color: white; text-decoration: none; }
.debug-info { background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; }
</style></head><body>";

echo "<h1>🔍 Universal Hybrid Extraction Audit</h1>";
echo "<p><strong>Objective:</strong> Verify hybrid party extraction works universally across all cases</p>";
echo "<hr>";

// Test cases from different institutions and types
$testCases = [
    [
        'numar' => '130/98/2022',
        'institutie' => 'TribunalulIALOMITA',
        'description' => 'Known working case (original test)',
        'expected' => '340+'
    ],
    [
        'numar' => '130/98/2022', 
        'institutie' => 'CurteadeApelBUCURESTI',
        'description' => 'User reported case (problematic)',
        'expected' => '340+'
    ],
    [
        'numar' => '1234/2023',
        'institutie' => 'TribunalulBUCURESTI',
        'description' => 'Different tribunal test',
        'expected' => '100+'
    ],
    [
        'numar' => '5678/2022',
        'institutie' => 'CurteadeApelCLUJ',
        'description' => 'Different appeal court test',
        'expected' => '100+'
    ],
    [
        'numar' => '999/2023',
        'institutie' => 'TribunalulCLUJ',
        'description' => 'Another tribunal test',
        'expected' => '100+'
    ]
];

echo "<div class='section'>";
echo "<h2>📊 Comprehensive Case Testing</h2>";
echo "<table class='audit-table'>";
echo "<tr>";
echo "<th>Case Number</th>";
echo "<th>Institution</th>";
echo "<th>Description</th>";
echo "<th>SOAP Parties</th>";
echo "<th>Decision Parties</th>";
echo "<th>Total Parties</th>";
echo "<th>Status</th>";
echo "<th>Test Links</th>";
echo "</tr>";

$dosarService = new DosarService();
$workingCases = 0;
$failedCases = 0;
$soapLimitedCases = 0;

foreach ($testCases as $testCase) {
    echo "<tr>";
    echo "<td><strong>{$testCase['numar']}</strong></td>";
    echo "<td>{$testCase['institutie']}</td>";
    echo "<td>{$testCase['description']}</td>";
    
    try {
        $dosar = $dosarService->getDetaliiDosar($testCase['numar'], $testCase['institutie']);
        
        if (!$dosar || empty($dosar->parti)) {
            echo "<td colspan='4'><span class='status-error'>❌ No data found</span></td>";
            $failedCases++;
        } else {
            $totalParties = count($dosar->parti);
            
            // Analyze party sources
            $soapCount = 0;
            $decisionCount = 0;
            $unknownCount = 0;
            
            foreach ($dosar->parti as $parte) {
                $source = $parte['source'] ?? 'unknown';
                switch ($source) {
                    case 'soap_api': $soapCount++; break;
                    case 'decision_text': $decisionCount++; break;
                    default: $unknownCount++; break;
                }
            }
            
            echo "<td>{$soapCount}</td>";
            echo "<td>{$decisionCount}</td>";
            echo "<td><strong>{$totalParties}</strong></td>";
            
            // Determine status
            if ($totalParties >= 300) {
                echo "<td><span class='status-success'>✅ Hybrid Success</span></td>";
                $workingCases++;
            } elseif ($totalParties == 100 && $soapCount == 100 && $decisionCount == 0) {
                echo "<td><span class='status-warning'>⚠️ SOAP Only</span></td>";
                $soapLimitedCases++;
            } elseif ($totalParties > 100) {
                echo "<td><span class='status-success'>✅ Partial Hybrid</span></td>";
                $workingCases++;
            } else {
                echo "<td><span class='status-error'>❌ Limited</span></td>";
                $failedCases++;
            }
        }
        
        // Test links
        $standardUrl = "detalii_dosar.php?numar=" . urlencode($testCase['numar']) . "&institutie=" . urlencode($testCase['institutie']);
        $debugUrl = $standardUrl . "&debug=1";
        
        echo "<td>";
        echo "<a href='{$standardUrl}' class='test-link' target='_blank'>View</a>";
        echo "<a href='{$debugUrl}' class='test-link' target='_blank'>Debug</a>";
        echo "</td>";
        
    } catch (Exception $e) {
        echo "<td colspan='4'><span class='status-error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</span></td>";
        echo "<td>-</td>";
        $failedCases++;
    }
    
    echo "</tr>";
}

echo "</table>";

// Summary
echo "<div class='debug-info'>";
echo "Test Summary:\n";
echo "Working cases (hybrid extraction): {$workingCases}\n";
echo "SOAP-only cases (no hybrid): {$soapLimitedCases}\n";
echo "Failed cases (no data): {$failedCases}\n";
echo "Total tested: " . count($testCases) . "\n";
echo "</div>";

if ($soapLimitedCases > 0) {
    echo "<div class='warning'>";
    echo "<h4>⚠️ SOAP-Only Cases Detected</h4>";
    echo "<p>{$soapLimitedCases} cases are showing only SOAP API data (100 parties) without hybrid extraction.</p>";
    echo "</div>";
}

echo "</div>";

// Test both legacy and PSR-4 implementations
echo "<div class='section'>";
echo "<h2>🔧 Implementation Comparison</h2>";
echo "<p>Testing the same case with both legacy and PSR-4 implementations:</p>";

$testCase = ['numar' => '130/98/2022', 'institutie' => 'TribunalulIALOMITA'];

echo "<table class='audit-table'>";
echo "<tr><th>Implementation</th><th>SOAP Parties</th><th>Decision Parties</th><th>Total Parties</th><th>Status</th></tr>";

// Test legacy implementation
try {
    $legacyDosar = $dosarService->getDetaliiDosar($testCase['numar'], $testCase['institutie']);
    $legacyTotal = count($legacyDosar->parti ?? []);
    
    $legacySoap = 0;
    $legacyDecision = 0;
    foreach ($legacyDosar->parti ?? [] as $parte) {
        $source = $parte['source'] ?? 'unknown';
        if ($source === 'soap_api') $legacySoap++;
        elseif ($source === 'decision_text') $legacyDecision++;
    }
    
    echo "<tr>";
    echo "<td><strong>Legacy (services/DosarService.php)</strong></td>";
    echo "<td>{$legacySoap}</td>";
    echo "<td>{$legacyDecision}</td>";
    echo "<td><strong>{$legacyTotal}</strong></td>";
    echo "<td>" . ($legacyTotal > 100 ? "<span class='status-success'>✅ Working</span>" : "<span class='status-error'>❌ Failed</span>") . "</td>";
    echo "</tr>";
} catch (Exception $e) {
    echo "<tr>";
    echo "<td><strong>Legacy (services/DosarService.php)</strong></td>";
    echo "<td colspan='4'><span class='status-error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</span></td>";
    echo "</tr>";
}

// Test PSR-4 implementation
try {
    // Load PSR-4 implementation
    if (file_exists('vendor/autoload.php')) {
        require_once 'vendor/autoload.php';
        $psr4DosarService = new \App\Services\DosarService();
        $psr4Dosar = $psr4DosarService->getDetaliiDosar($testCase['numar'], $testCase['institutie']);
        $psr4Total = count($psr4Dosar->parti ?? []);
        
        $psr4Soap = 0;
        $psr4Decision = 0;
        foreach ($psr4Dosar->parti ?? [] as $parte) {
            $source = $parte['source'] ?? 'unknown';
            if ($source === 'soap_api') $psr4Soap++;
            elseif ($source === 'decision_text') $psr4Decision++;
        }
        
        echo "<tr>";
        echo "<td><strong>PSR-4 (src/Services/DosarService.php)</strong></td>";
        echo "<td>{$psr4Soap}</td>";
        echo "<td>{$psr4Decision}</td>";
        echo "<td><strong>{$psr4Total}</strong></td>";
        echo "<td>" . ($psr4Total > 100 ? "<span class='status-success'>✅ Working</span>" : "<span class='status-error'>❌ Failed</span>") . "</td>";
        echo "</tr>";
        
        // Compare implementations
        if ($legacyTotal !== $psr4Total) {
            echo "<tr style='background-color: #fff3cd;'>";
            echo "<td colspan='5'><strong>⚠️ INCONSISTENCY DETECTED:</strong> Legacy shows {$legacyTotal} parties, PSR-4 shows {$psr4Total} parties</td>";
            echo "</tr>";
        }
    } else {
        echo "<tr>";
        echo "<td><strong>PSR-4 (src/Services/DosarService.php)</strong></td>";
        echo "<td colspan='4'><span class='status-warning'>⚠️ PSR-4 autoloader not available</span></td>";
        echo "</tr>";
    }
} catch (Exception $e) {
    echo "<tr>";
    echo "<td><strong>PSR-4 (src/Services/DosarService.php)</strong></td>";
    echo "<td colspan='4'><span class='status-error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</span></td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Method verification
echo "<div class='section'>";
echo "<h2>🔍 Method Verification</h2>";
echo "<p>Verifying that hybrid extraction methods exist and are properly implemented:</p>";

$methods = [
    'extractPartiesFromDecisionText',
    'mergeAndDeduplicateParties',
    'mapDosarToObject'
];

echo "<table class='audit-table'>";
echo "<tr><th>Method</th><th>Legacy Implementation</th><th>PSR-4 Implementation</th></tr>";

foreach ($methods as $method) {
    echo "<tr>";
    echo "<td><strong>{$method}</strong></td>";
    
    // Check legacy
    if (method_exists($dosarService, $method)) {
        echo "<td><span class='status-success'>✅ Exists</span></td>";
    } else {
        echo "<td><span class='status-error'>❌ Missing</span></td>";
    }
    
    // Check PSR-4
    if (isset($psr4DosarService) && method_exists($psr4DosarService, $method)) {
        echo "<td><span class='status-success'>✅ Exists</span></td>";
    } else {
        echo "<td><span class='status-error'>❌ Missing</span></td>";
    }
    
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Recommendations
echo "<div class='section'>";
echo "<h2>💡 Analysis and Recommendations</h2>";

if ($soapLimitedCases > 0) {
    echo "<div class='error'>";
    echo "<h4>❌ Critical Issue Identified</h4>";
    echo "<p><strong>{$soapLimitedCases} out of " . count($testCases) . " cases</strong> are not using hybrid extraction and are limited to 100 parties from SOAP API only.</p>";
    echo "<p><strong>Root Cause:</strong> The hybrid extraction system is not being applied universally.</p>";
    echo "</div>";
    
    echo "<h4>Immediate Actions Required:</h4>";
    echo "<ol>";
    echo "<li><strong>Verify method implementation:</strong> Ensure extractPartiesFromDecisionText() and mergeAndDeduplicateParties() are working</li>";
    echo "<li><strong>Check decision text availability:</strong> Some cases may not have decision text to parse</li>";
    echo "<li><strong>Institution-specific issues:</strong> Some institutions may return different data structures</li>";
    echo "<li><strong>Error handling:</strong> Silent failures in hybrid extraction methods</li>";
    echo "</ol>";
} else {
    echo "<div class='success'>";
    echo "<h4>✅ Hybrid Extraction Working</h4>";
    echo "<p>All tested cases are successfully using hybrid extraction or have valid reasons for limited parties.</p>";
    echo "</div>";
}

echo "<h4>Next Steps:</h4>";
echo "<ul>";
echo "<li>Test the problematic case: 130/98/2022 from CurteadeApelBUCURESTI</li>";
echo "<li>Verify institution parameter format compatibility</li>";
echo "<li>Check error logs for silent failures in hybrid extraction</li>";
echo "<li>Ensure both legacy and PSR-4 implementations produce identical results</li>";
echo "</ul>";

echo "</div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    console.log('🔍 Universal Hybrid Extraction Audit - Complete');";
echo "    console.log('Working cases: {$workingCases}');";
echo "    console.log('SOAP-only cases: {$soapLimitedCases}');";
echo "    console.log('Failed cases: {$failedCases}');";
echo "    ";
echo "    if ({$soapLimitedCases} > 0) {";
echo "        console.warn('⚠️ CRITICAL: ' + {$soapLimitedCases} + ' cases not using hybrid extraction');";
echo "    } else {";
echo "        console.log('✅ All cases using hybrid extraction successfully');";
echo "    }";
echo "});";
echo "</script>";

echo "</body></html>";
?>
