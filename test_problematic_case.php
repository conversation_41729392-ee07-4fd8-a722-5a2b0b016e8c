<?php
/**
 * Test Problematic Case
 * Direct testing of the specific case reported by user: 130/98/2022 from CurteadeApelBUCURESTI
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Problematic Case Test</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.debug-info { background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap; }
.comparison { display: flex; gap: 20px; }
.comparison > div { flex: 1; }
.test-link { display: inline-block; margin: 2px; padding: 4px 8px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; font-size: 12px; }
.test-link:hover { background: #0056b3; color: white; text-decoration: none; }
</style></head><body>";

echo "<h1>🚨 Problematic Case Investigation</h1>";
echo "<p><strong>Case:</strong> 130/98/2022 from CurteadeApelBUCURESTI</p>";
echo "<p><strong>Issue:</strong> User reports this case shows limited parties despite hybrid extraction</p>";
echo "<hr>";

// Test both the working case and the problematic case
$testCases = [
    [
        'numar' => '130/98/2022',
        'institutie' => 'TribunalulIALOMITA',
        'description' => 'Known working case',
        'expected' => 'Should show 340+ parties'
    ],
    [
        'numar' => '130/98/2022',
        'institutie' => 'CurteadeApelBUCURESTI',
        'description' => 'User reported problematic case',
        'expected' => 'User reports limited parties'
    ]
];

echo "<div class='section'>";
echo "<h2>🔍 Comparative Analysis</h2>";

$dosarService = new DosarService();

echo "<div class='comparison'>";

foreach ($testCases as $index => $testCase) {
    echo "<div>";
    echo "<h3>" . ($index + 1) . ". {$testCase['description']}</h3>";
    echo "<p><strong>Case:</strong> {$testCase['numar']}</p>";
    echo "<p><strong>Institution:</strong> {$testCase['institutie']}</p>";
    echo "<p><strong>Expected:</strong> {$testCase['expected']}</p>";
    
    try {
        // Test the case
        $dosar = $dosarService->getDetaliiDosar($testCase['numar'], $testCase['institutie']);
        
        if (!$dosar || empty($dosar->parti)) {
            echo "<div class='error'>";
            echo "<h4>❌ No Data Found</h4>";
            echo "<p>The case returned no data or no parties.</p>";
            echo "</div>";
        } else {
            $totalParties = count($dosar->parti);
            
            // Analyze party sources
            $soapCount = 0;
            $decisionCount = 0;
            $unknownCount = 0;
            
            foreach ($dosar->parti as $parte) {
                $source = $parte['source'] ?? 'unknown';
                switch ($source) {
                    case 'soap_api': $soapCount++; break;
                    case 'decision_text': $decisionCount++; break;
                    default: $unknownCount++; break;
                }
            }
            
            echo "<div class='debug-info'>";
            echo "Results Summary:
Total Parties: {$totalParties}
SOAP API Parties: {$soapCount}
Decision Text Parties: {$decisionCount}
Unknown Source: {$unknownCount}

Case Details:
Number: " . ($dosar->numar ?? 'N/A') . "
Institution: " . ($dosar->institutie ?? 'N/A') . "
Object: " . (isset($dosar->obiect) ? substr($dosar->obiect, 0, 100) . '...' : 'N/A') . "
Date: " . ($dosar->data ?? 'N/A') . "
Sessions: " . count($dosar->sedinte ?? []) . "
";
            echo "</div>";
            
            // Status determination
            if ($totalParties >= 300) {
                echo "<div class='success'>";
                echo "<h4>✅ Hybrid Extraction Working</h4>";
                echo "<p>Found {$totalParties} parties using hybrid extraction.</p>";
                echo "</div>";
            } elseif ($totalParties == 100 && $soapCount == 100 && $decisionCount == 0) {
                echo "<div class='warning'>";
                echo "<h4>⚠️ SOAP-Only (No Hybrid)</h4>";
                echo "<p>Only {$totalParties} parties found - hybrid extraction not working.</p>";
                echo "</div>";
            } elseif ($totalParties > 100) {
                echo "<div class='success'>";
                echo "<h4>✅ Partial Hybrid Working</h4>";
                echo "<p>Found {$totalParties} parties - hybrid extraction partially working.</p>";
                echo "</div>";
            } else {
                echo "<div class='error'>";
                echo "<h4>❌ Limited Data</h4>";
                echo "<p>Only {$totalParties} parties found - below expected threshold.</p>";
                echo "</div>";
            }
            
            // Show sample parties
            if ($totalParties > 0) {
                echo "<h5>Sample Parties (first 10):</h5>";
                echo "<div class='debug-info'>";
                for ($i = 0; $i < min(10, $totalParties); $i++) {
                    $parte = $dosar->parti[$i];
                    echo ($i + 1) . ". " . ($parte['nume'] ?? 'N/A') . " (" . ($parte['calitate'] ?? 'N/A') . ") [" . ($parte['source'] ?? 'unknown') . "]\n";
                }
                if ($totalParties > 10) {
                    echo "... and " . ($totalParties - 10) . " more parties\n";
                }
                echo "</div>";
            }
        }
        
        // Test links
        $standardUrl = "detalii_dosar.php?numar=" . urlencode($testCase['numar']) . "&institutie=" . urlencode($testCase['institutie']);
        $debugUrl = $standardUrl . "&debug=1";
        
        echo "<p><strong>Test Links:</strong></p>";
        echo "<a href='{$standardUrl}' class='test-link' target='_blank'>View Case</a>";
        echo "<a href='{$debugUrl}' class='test-link' target='_blank'>Debug Mode</a>";
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h4>❌ Error</h4>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Institution parameter investigation
echo "<div class='section'>";
echo "<h2>🔧 Institution Parameter Investigation</h2>";
echo "<p>Testing different institution parameter formats for the problematic case:</p>";

$institutionVariants = [
    'CurteadeApelBUCURESTI',
    'Curtea de Apel BUCURESTI',
    'CURTEA DE APEL BUCURESTI',
    'CurteadeApelBucuresti',
    'BUCURESTI_CA',
    'CA_BUCURESTI'
];

echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px; border: 1px solid #ddd;'>Institution Parameter</th>";
echo "<th style='padding: 8px; border: 1px solid #ddd;'>Result</th>";
echo "<th style='padding: 8px; border: 1px solid #ddd;'>Parties Count</th>";
echo "<th style='padding: 8px; border: 1px solid #ddd;'>Status</th>";
echo "</tr>";

foreach ($institutionVariants as $institution) {
    echo "<tr>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>{$institution}</strong></td>";
    
    try {
        $dosar = $dosarService->getDetaliiDosar('130/98/2022', $institution);
        
        if (!$dosar || empty($dosar->parti)) {
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>No data</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>0</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>❌ Failed</td>";
        } else {
            $totalParties = count($dosar->parti);
            echo "<td style='padding: 8px; border: 1px solid #ddd;'>Data found</td>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>{$totalParties}</strong></td>";
            
            if ($totalParties >= 300) {
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>✅ Hybrid working</td>";
            } elseif ($totalParties == 100) {
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>⚠️ SOAP only</td>";
            } else {
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>❓ Limited</td>";
            }
        }
    } catch (Exception $e) {
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>Error</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>-</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>❌ Exception</td>";
    }
    
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Raw SOAP investigation
echo "<div class='section'>";
echo "<h2>🔍 Raw SOAP Investigation</h2>";
echo "<p>Direct SOAP API call to understand the data structure:</p>";

try {
    $reflection = new ReflectionClass($dosarService);
    $executeSoapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $executeSoapMethod->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => '130/98/2022',
        'institutie' => 'CurteadeApelBUCURESTI',
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $response = $executeSoapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Raw investigation");
    
    echo "<div class='debug-info'>";
    echo "Raw SOAP Response Analysis:\n";
    
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        echo "Response contains Dosar data: YES\n";
        echo "Dosar is array: " . (is_array($dosare) ? 'YES' : 'NO') . "\n";
        
        $rawDosar = is_array($dosare) ? $dosare[0] : $dosare;
        echo "Dosar number: " . ($rawDosar->numar ?? 'N/A') . "\n";
        echo "Dosar institution: " . ($rawDosar->institutie ?? 'N/A') . "\n";
        echo "Has parties: " . (isset($rawDosar->parti) ? 'YES' : 'NO') . "\n";
        
        if (isset($rawDosar->parti)) {
            echo "Has DosarParte: " . (isset($rawDosar->parti->DosarParte) ? 'YES' : 'NO') . "\n";
            if (isset($rawDosar->parti->DosarParte)) {
                $soapParties = $rawDosar->parti->DosarParte;
                $soapCount = is_array($soapParties) ? count($soapParties) : 1;
                echo "SOAP parties count: {$soapCount}\n";
                
                if ($soapCount >= 100) {
                    echo "⚠️ SOAP API limit reached - hybrid extraction should activate\n";
                } else {
                    echo "ℹ️ SOAP API limit not reached - hybrid extraction may not be needed\n";
                }
            }
        }
        
        // Check for decision text
        echo "Has sedinte: " . (isset($rawDosar->sedinte) ? 'YES' : 'NO') . "\n";
        if (isset($rawDosar->sedinte) && isset($rawDosar->sedinte->DosarSedinta)) {
            $sedinte = $rawDosar->sedinte->DosarSedinta;
            $sedinteCount = is_array($sedinte) ? count($sedinte) : 1;
            echo "Sessions count: {$sedinteCount}\n";
            
            // Check for decision text in sessions
            $hasDecisionText = false;
            if (is_array($sedinte)) {
                foreach ($sedinte as $sedinta) {
                    if (!empty($sedinta->solutie) || !empty($sedinta->solutieSumar)) {
                        $hasDecisionText = true;
                        break;
                    }
                }
            } else {
                if (!empty($sedinte->solutie) || !empty($sedinte->solutieSumar)) {
                    $hasDecisionText = true;
                }
            }
            echo "Has decision text: " . ($hasDecisionText ? 'YES' : 'NO') . "\n";
        }
        
    } else {
        echo "Response contains Dosar data: NO\n";
        echo "Response structure: " . print_r($response, true) . "\n";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h4>❌ Raw SOAP Investigation Failed</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Conclusions and recommendations
echo "<div class='section'>";
echo "<h2>📋 Investigation Conclusions</h2>";

echo "<h4>Key Findings:</h4>";
echo "<ul>";
echo "<li>Compare the working case (TribunalulIALOMITA) vs problematic case (CurteadeApelBUCURESTI)</li>";
echo "<li>Check if institution parameter format affects data retrieval</li>";
echo "<li>Verify if SOAP API returns different data structures for different institutions</li>";
echo "<li>Confirm if decision text is available for hybrid extraction</li>";
echo "</ul>";

echo "<h4>Next Steps:</h4>";
echo "<ol>";
echo "<li>If both cases show similar party counts, the issue may be in frontend display</li>";
echo "<li>If the problematic case shows fewer parties, the issue is in backend processing</li>";
echo "<li>Check if institution-specific data structures require different parsing logic</li>";
echo "<li>Verify error logs for any silent failures during hybrid extraction</li>";
echo "</ol>";

echo "<h4>Test the actual frontend:</h4>";
echo "<p>Visit the actual case pages to see what users see:</p>";
echo "<a href='detalii_dosar.php?numar=130%2F98%2F2022&institutie=TribunalulIALOMITA' class='test-link' target='_blank'>Working Case (TribunalulIALOMITA)</a>";
echo "<a href='detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI' class='test-link' target='_blank'>Problematic Case (CurteadeApelBUCURESTI)</a>";

echo "</div>";

echo "</body></html>";
?>
